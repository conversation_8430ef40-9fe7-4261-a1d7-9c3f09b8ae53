<template>
  <div class="staff-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">员工信息管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理员工基本信息、资质证书、培训记录和考核档案</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="info"
            @click="handleDepartmentManagement"
            class="bg-gray-500 hover:bg-gray-600 border-gray-500"
          >
            <el-icon class="mr-2">
              <Setting />
            </el-icon>
            部门管理
          </el-button>
          <el-button
            type="primary"
            @click="handleAddStaff"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增员工
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <StaffTable
      ref="staffTableRef"
      :filters="currentFilters"
      @edit="handleEditStaff"
      @row-click="handleRowClick"
      @manage-certificate="handleManageCertificate"
      @manage-training="handleManageTraining"
      @manage-assessment="handleManageAssessment"
      @manage-health="handleManageHealth"
      @delete="handleDeleteStaff"
    />

    <!-- 员工表单弹窗 -->
    <StaffFormDialog
      v-model:visible="formVisible"
      :item-id="currentStaff?.sid"
      :mode="formMode"
      @save="handleSaveStaff"
    />

    <!-- 员工详情弹窗 -->
    <StaffDetailDialog
      v-model:visible="detailVisible"
      :item-id="currentStaff?.sid"
      @edit="handleEditFromDetail"
    />

    <!-- 资质证书管理弹窗 -->
    <StaffCertificateDialog
      v-model:visible="certificateVisible"
      :staff-data="currentStaff"
      @save="handleSaveCertificate"
    />

    <!-- 培训记录管理弹窗 -->
    <StaffTrainingDialog
      v-model:visible="trainingVisible"
      :staff-data="currentStaff"
      @save="handleSaveTraining"
    />

    <!-- 考核记录管理弹窗 -->
    <StaffAssessmentDialog
      v-model:visible="assessmentVisible"
      :staff-data="currentStaff"
      @save="handleSaveAssessment"
    />

    <!-- 健康检查记录弹窗 -->
    <StaffHealthDialog
      v-model:visible="healthVisible"
      :staff-data="currentStaff"
      @save="handleSaveHealth"
    />

    <!-- 部门管理弹窗 -->
    <el-dialog
      v-model="departmentVisible"
      title="部门管理"
      width="800px"
      align-center
      :before-close="handleCloseDepartmentDialog"
      :close-on-click-modal="false"
      class="department-dialog"
    >
      <div class="department-management">
        <!-- 操作按钮 -->
        <div class="mb-4 flex justify-end">
          <el-button
            type="primary"
            @click="handleAddDepartment"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增部门
          </el-button>
        </div>

        <!-- 部门列表 -->
        <el-table
          :data="departmentList"
          v-loading="departmentLoading"
          stripe
          class="w-full"
          :header-cell-style="{
            backgroundColor: '#f9fafb',
            color: '#374151',
            fontWeight: '600',
            borderBottom: '1px solid #e5e7eb',
            textAlign: 'center',
          }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="name" label="部门名称" min-width="200" />
          <el-table-column prop="created_at" label="创建时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleEditDepartment(row)"
                class="mr-2"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDeleteDepartment(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 部门表单弹窗 -->
    <el-dialog
      v-model="departmentFormVisible"
      :title="departmentFormMode === 'add' ? '新增部门' : '编辑部门'"
      width="500px"
      align-center
      :before-close="handleCloseDepartmentForm"
      :close-on-click-modal="false"
      class="department-form-dialog"
    >
      <el-form
        ref="departmentFormRef"
        :model="departmentForm"
        :rules="departmentRules"
        label-width="100px"
        class="department-form"
      >
        <el-form-item label="部门名称" prop="name">
          <el-input
            v-model="departmentForm.name"
            placeholder="请输入部门名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDepartmentForm">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmitDepartment"
            :loading="departmentSubmitting"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            {{ departmentFormMode === 'add' ? '创建' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElButton, ElMessage, ElIcon, ElDialog, ElTable, ElTableColumn, ElForm, ElFormItem, ElInput, ElMessageBox } from 'element-plus'
import { Plus, Setting } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, post, put, del } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'
import FilterPanel from '@/components/FilterPanel.vue'
import StaffTable from './components/StaffTable.vue'
import StaffFormDialog from './components/StaffFormDialog.vue'
import StaffDetailDialog from './components/StaffDetailDialog.vue'
import StaffCertificateDialog from './components/StaffCertificateDialog.vue'
import StaffTrainingDialog from './components/StaffTrainingDialog.vue'
import StaffAssessmentDialog from './components/StaffAssessmentDialog.vue'
import StaffHealthDialog from './components/StaffHealthDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { computed } from 'vue'

// 使用基础数据store
const baseDataStore = useBaseDataStore()

// 响应式数据
const formVisible = ref(false)
const detailVisible = ref(false)
const certificateVisible = ref(false)
const trainingVisible = ref(false)
const assessmentVisible = ref(false)
const healthVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentStaff = ref(null)

// 部门管理相关数据
const departmentVisible = ref(false)
const departmentFormVisible = ref(false)
const departmentFormMode = ref('add') // 'add' | 'edit'
const departmentLoading = ref(false)
const departmentSubmitting = ref(false)
const departmentList = ref([])
const currentDepartment = ref(null)
const departmentFormRef = ref(null)

// 部门表单数据
const departmentForm = reactive({
  name: ''
})

// 部门表单验证规则
const departmentRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 1, max: 50, message: '部门名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取 table 组件引用
const staffTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  department: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'department',
    type: 'select',
    label: '所属部门',
    placeholder: '请选择部门',
    options: baseDataStore.departments.getOptions(),
  },
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入姓名或员工号',
  },
])

// 新增员工
const handleAddStaff = () => {
  currentStaff.value = null
  formMode.value = 'add'
  formVisible.value = true
}

// 编辑员工
const handleEditStaff = (staff) => {
  currentStaff.value = staff
  formMode.value = 'edit'
  formVisible.value = true
}

// 从详情弹窗编辑员工
const handleEditFromDetail = (staff) => {
  currentStaff.value = staff
  formMode.value = 'edit'
  detailVisible.value = false
  formVisible.value = true
}

// 表单提交成功
const handleSaveStaff = () => {
  formVisible.value = false
  currentStaff.value = null
  staffTableRef.value?.refresh() // 刷新表格数据
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  staffTableRef.value?.resetPagination()
}

// 行点击 - 查看详情
const handleRowClick = (staff) => {
  currentStaff.value = staff
  detailVisible.value = true
}

// 管理证书
const handleManageCertificate = (staff) => {
  currentStaff.value = staff
  certificateVisible.value = true
}

// 管理培训
const handleManageTraining = (staff) => {
  currentStaff.value = staff
  trainingVisible.value = true
}

// 管理考核
const handleManageAssessment = (staff) => {
  currentStaff.value = staff
  assessmentVisible.value = true
}

// 管理健康
const handleManageHealth = (staff) => {
  currentStaff.value = staff
  healthVisible.value = true
}

// 删除员工 - 直接在表格组件中处理
const handleDeleteStaff = () => {
  // 这个方法由表格组件处理，这里只是为了保持接口一致性
}

const handleSaveCertificate = () => {
  ElMessage.success('证书信息保存成功')
  certificateVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveTraining = () => {
  ElMessage.success('培训记录保存成功')
  trainingVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveAssessment = () => {
  ElMessage.success('考核记录保存成功')
  assessmentVisible.value = false
  staffTableRef.value?.refresh()
}

const handleSaveHealth = () => {
  ElMessage.success('健康检查记录保存成功')
  healthVisible.value = false
  staffTableRef.value?.refresh()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 部门管理相关方法
const handleDepartmentManagement = () => {
  departmentVisible.value = true
  loadDepartmentList()
}

// 加载部门列表
const loadDepartmentList = async () => {
  departmentLoading.value = true
  try {
    const response = await get('maternity-center/department/list/')
    departmentList.value = response.data || []
  } catch (error) {
    showErrorTip(error)
    departmentList.value = []
  } finally {
    departmentLoading.value = false
  }
}

// 新增部门
const handleAddDepartment = () => {
  departmentFormMode.value = 'add'
  currentDepartment.value = null
  departmentForm.name = ''
  departmentFormVisible.value = true
}

// 编辑部门
const handleEditDepartment = (department) => {
  departmentFormMode.value = 'edit'
  currentDepartment.value = department
  departmentForm.name = department.name
  departmentFormVisible.value = true
}

// 删除部门
const handleDeleteDepartment = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门"${department.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await del(`maternity-center/department/delete/${department.rid}/`)
    ElMessage.success('部门删除成功')
    loadDepartmentList()

    // 刷新基础数据store中的部门数据
    baseDataStore.departments.fetch()
  } catch (error) {
    if (error !== 'cancel') {
      showErrorTip(error)
    }
  }
}

// 提交部门表单
const handleSubmitDepartment = async () => {
  try {
    await departmentFormRef.value.validate()

    departmentSubmitting.value = true

    const submitData = {
      name: departmentForm.name
    }

    if (departmentFormMode.value === 'add') {
      await post('maternity-center/department/create/', submitData)
      ElMessage.success('部门创建成功')
    } else {
      await put(`maternity-center/department/update/${currentDepartment.value.rid}/`, submitData)
      ElMessage.success('部门更新成功')
    }

    departmentFormVisible.value = false
    loadDepartmentList()

    // 刷新基础数据store中的部门数据
    baseDataStore.departments.fetch()
  } catch (error) {
    showErrorTip(error)
  } finally {
    departmentSubmitting.value = false
  }
}

// 关闭部门管理对话框
const handleCloseDepartmentDialog = () => {
  departmentVisible.value = false
}

// 关闭部门表单对话框
const handleCloseDepartmentForm = () => {
  if (departmentSubmitting.value) return
  departmentFormVisible.value = false
}
</script>

<style scoped>
.staff-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.department-dialog {
  --el-dialog-border-radius: 12px;
}

.department-management {
  padding: 0 8px;
}

.department-form-dialog {
  --el-dialog-border-radius: 12px;
}

.department-form {
  padding: 0 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
